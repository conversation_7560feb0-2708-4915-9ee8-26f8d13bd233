import { createContext, useContext,  SetStateAction, useState, Dispatch, useEffect, ReactNode } from 'react'
import {getAuth, User, signInWithEmailAndPassword, createUserWithEmailAndPassword, signInWithPhoneNumber, signInAnonymously, RecaptchaVerifier, UserCredential} from 'firebase/auth'

interface UserProtoTypes {
    currentUser: User | null;
    setCurrentUser: Dispatch<SetStateAction<User | null>>;
    signOut: () => void;
    signUp: (email: string, password: string) => void;
    logIn: (email: string, password: string) => User | null;
    getUser: () => User | null;
    phoneAuth: (phoneNumber: string, verifier: RecaptchaVerifier | undefined) => void;
    phoneCodeAuth: (code: string) => void;
    signInAnonymously: () => Promise<User | null>;
}

type Props = {
    children: ReactNode
  }

const AuthContext = createContext<(UserProtoTypes)>({
    currentUser: null,
    setCurrentUser: () => {return},
    signOut: () => {return},
    signUp: () => {return},
    logIn:() => {return null},
    getUser: () => {return null},
    phoneAuth: () => {return},
    phoneCodeAuth: () => {return},
    signInAnonymously: async () => {return null},
});

export function useAuth() {
  return useContext(AuthContext)
}

export function AuthProvider({children}: Props):JSX.Element{
  const auth = getAuth();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true)

  const logIn = (email: string, password: string) => {
    console.log("AuthProvider logIn");

    signInWithEmailAndPassword(auth, email, password).then(
        (userCredential) => {
            console.log(userCredential.user);
            console.log("AuthProvider logged in as: ", currentUser);
            setCurrentUser(userCredential.user);
            window.location.href = "/";
        }
    ).catch(error => {
        console.error(error);
    });

    return currentUser;
  };

  const signOut = () => {
    return auth.signOut();
  }

  const signUp = (email: string, password: string) => {
    return createUserWithEmailAndPassword(auth, email, password)
  }


  const getUser = () => {
    return auth.currentUser ? auth.currentUser : null;
  }

  const signInAnonymouslyMethod = async () => {
    console.log("signInAnonymously");

    try {
      const userCredential = await signInAnonymously(auth);
      const user = userCredential.user;
      console.log("Anonymous Auth: ", user);
      setCurrentUser(user);
      console.log("Logged in as currentUser: ", currentUser);
      return user;
    } catch (error: any) {
      const errorCode = error.code;
      const errorMessage = error.message;
      console.error("Anonymous auth error:", errorCode, errorMessage);
      throw error;
    }
  }

  // Auth via phone code

  function phoneAuth(phoneNumber: string, verifier: RecaptchaVerifier | undefined) {
    console.log("phoneAuth: ", phoneNumber);

    if(verifier){
        console.log("phoneAuth verifier: ", verifier);
        signInWithPhoneNumber(auth, phoneNumber, verifier)
            .then((confirmationResult) => {
            // SMS sent. Prompt user to type the code from the message, then sign the
            // user in with confirmationResult.confirm(code).
            window.confirmationResult = confirmationResult;
            console.log("phoneAuth: ", confirmationResult);
            
            }).catch((error) => {
                // Error; SMS not sent
                // ...
                console.error(error);
                window.confirmationResult = undefined;

            });
    } else {
        console.log("phoneAuth verifier: ", verifier);
    }    
  }

  function phoneCodeAuth(code: string) {
    console.log("phoneCodeAuth: ", code);
    console.log('AuthProvider phoneCodeAuth window.confirmationResult', window.confirmationResult);
    
    window.confirmationResult?.confirm(code).then((result: UserCredential) => {
        // User signed in successfully.
        const user = result.user;
        // ...
        console.log("phoneCodeAuth: ", user);
        setCurrentUser(user);
        console.log("Logged in as currentUser: ", currentUser);
        
      }).catch((error: unknown) => {
        // User couldn't sign in (bad verification code?)
        // ...
        console.error(error);
      });
  }

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(user => {

      if(user) {
        console.log("AuthContext onAuthStateChanged user: ", user);
        setCurrentUser(user)
        console.log("AuthContext onAuthStateChanged Logged in as: ", user);
      }
      setLoading(false)
    });

    return unsubscribe
  }, [])

  const value = {
    auth,
    currentUser,
    getUser,
    logIn,
    signOut,
    signUp,
    setCurrentUser,
    phoneAuth,
    phoneCodeAuth,
    signInAnonymously: signInAnonymouslyMethod
  }

  return (
    <AuthContext.Provider value={value}>
      { !loading && children}
    </AuthContext.Provider>
  )

}