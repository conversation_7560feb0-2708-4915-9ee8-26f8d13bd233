import { useState, useEffect } from "react";
import { User } from "firebase/auth";
import { useAuth } from './contexts/AuthContext';
import { getFirestore, doc, getDoc } from 'firebase/firestore';
import type { TherapistProfile } from './Therapists/types';


const HeaderNav = () => {
  const { signOut, getUser } = useAuth();
  const [currentUser, setCurrentUser] = useState<null|User>(getUser());
  const logOut = () => {
    console.log("logOut");
    if(currentUser) {
      signOut();
      setCurrentUser(null);
    }
  }

  // const logIn = () => {
  //   // show login modal
  //   console.log("logIn");
  //   openInNewTab("/login", false);
  // }

  // const SignUp = () => {
  //   // show signup modal
  //   console.log("SignUp");
  //   openInNewTab("/signup", false);
  // }

  // const openInNewTab = (url : string, blank: boolean) => {
  //   if(blank) {
  //     window.open(url, "_blank", "noreferrer");
  //   } else {
  //     location.href = url;
  //   }
  // };

  const [width, setWidth] = useState(window.innerWidth);
  const [showNavMenu, setShowNavMenu] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [profileData, setProfileData] = useState<TherapistProfile | null>(null);

  useEffect(() => {
    const handleResize = () => setWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);

    if (width < 480) {
      setShowNavMenu(false);
    } else {
      setShowNavMenu(true)
    }

    return () => window.removeEventListener("resize", handleResize);

  }, [width]);

  useEffect(() => {
    if (currentUser) {
      const fetchProfile = async () => {
        const db = getFirestore();
        const profileRef = doc(db, 'therapists', currentUser.uid);
        const profileSnapshot = await getDoc(profileRef);
        if (profileSnapshot.exists()) {
          setProfileData(profileSnapshot.data() as TherapistProfile);
        }
      };
      fetchProfile();
    }
  }, [currentUser]);

  return (
    <div>
        <nav id="header" className="w-full z-30 top-0 text-white py-1 lg:py-6">
          <div
            className="w-full container mx-auto flex flex-wrap items-center justify-between mt-0 px-2 py-2 lg:py-6"
          >
            <div className="pl-4 flex items-center">
              <a
                className="text-white no-underline hover:no-underline font-bold text-2xl lg:text-4xl"
                href="/"
              >
                  <span className="inline-flex items-baseline">
                      <img src="/brain-solid-svgrepo-com.svg" alt="Neuro Logo" width="40" className="self-center w-35 h-35 rounded-full mx-1 filter-white"  />
                      <span>Neuro</span>
                  </span>
              </a>
            </div>

            <div className="block lg:hidden pr-4">
              <button
                id="nav-toggle"
                onClick={() => setShowNavMenu(!showNavMenu)}
                className="flex items-center px-3 py-2 border rounded text-gray-100 border-gray-600 hover:text-gray-800 hover:border-green-500 appearance-none focus:outline-none"
              >
                <svg
                  className="fill-current h-3 w-3"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Menu</title>
                  <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z" />
                </svg>
              </button>
            </div>

            <div
              className={showNavMenu ? "w-full flex-grow lg:flex lg:items-center lg:w-auto lg:block mt-2 lg:mt-0 text-white p-4 lg:p-0 z-20" : "w-full flex-grow lg:flex lg:items-center lg:w-auto hidden lg:block mt-2 lg:mt-0 text-white p-4 lg:p-0 z-20"}
              id="nav-content"
            >
              <ul className="list-reset lg:flex justify-end flex-1 items-center">
                <li className="mr-3">
                  <a
                    className="inline-block py-2 px-4 text-white no-underline hover:text-gray-800 hover:text-underline font-bold"
                    href="/therapists"
                    >For Therapists</a>
                </li>
                <li className="mr-3">
                  <a
                    className="inline-block text-white no-underline hover:text-gray-800 hover:text-underline py-2 px-4 font-bold"
                    href="/about"
                    >For Anyone</a>
                </li>
                {currentUser ? 
                <li className="mr-3 relative">
                    <div className="relative">
                        <button
                            onClick={() => setShowDropdown(!showDropdown)}
                            className="flex items-center space-x-2 text-white hover:text-gray-800 py-2 px-4 font-bold"
                        >
                            <img 
                                src={profileData?.image_url || currentUser?.photoURL || '/profile1-female.png'} 
                                alt="Profile" 
                                className="w-8 h-8 rounded-full object-cover"
                            />
                            <span>{profileData?.name || currentUser?.email}</span>
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        
                        {showDropdown && (
                            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                
                                {profileData ? (
                                    <>
                                <a 
                                    href="/matches" 
                                    className="block px-4 py-2 text-gray-800 hover:bg-gray-100"
                                >
                                    Match Requests
                                </a>

                                </>) : ""}
                                
                                {profileData ? (
                                    <>
                                        <a 
                                            href="/therapists/profile" 
                                            className="block px-4 py-2 text-gray-800 hover:bg-gray-100"
                                        >
                                            My Profile
                                        </a>
                                        <a 
                                            href="/therapists/edit-profile" 
                                            className="block px-4 py-2 text-gray-800 hover:bg-gray-100"
                                        >
                                            Edit Profile
                                        </a>
                                    </>
                                ) : (
                                    <a 
                                        href="/therapists/create-profile" 
                                        className="block px-4 py-2 text-gray-800 hover:bg-gray-100"
                                    >
                                        Create Profile
                                    </a>
                                )}
                                <button
                                    onClick={logOut}
                                    className="block w-full text-left px-4 py-2 text-gray-800 hover:bg-gray-100"
                                >
                                    Sign Out
                                </button>
                            </div>
                        )}
                    </div>
                </li>  : 
                <><li className="mr-3">
                  {/* <a
                    className="inline-block text-white no-underline hover:text-gray-800 hover:text-underline py-2 px-4 font-bold"
                    href="/login"
                  >Log In</a> */}
                </li></> 
                }
              </ul>
            </div>
          </div>
        </nav>
    </div>
  );
};

export default HeaderNav;
