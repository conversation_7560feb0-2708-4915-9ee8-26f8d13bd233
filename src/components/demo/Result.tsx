import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { db } from '../Firebase';
import { doc, getDoc } from 'firebase/firestore';
import Markdown from 'react-markdown';
import { ShowMore } from '@re-dev/react-truncate';
import { TherapistDoc, TherapyDoc } from './MoodAssessment';
import MatchingModal from './MatchingModal';
import { useAuth } from '../contexts/AuthContext';
import ConditionClassification from './ConditionClassification';

// interface ResultParams {
//   id: string;
// }

interface AIAssessmentResults {
  aiRecommendations: string;
  therapists: string[];
  therapies: string[];
  aiTherapyRecommendations: string;
  timestamp: number;
}

interface AssessmentResultRecord {
  request: {
    moodScore: number;
    concerns: string[];
    note: string;
    preferences: any;
    profile: any;
  };
  results: AIAssessmentResults;
  createdAt: any;
}

const Result = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<AssessmentResultRecord | null>(null);
  const [activeTab, setActiveTab] = useState('therapies');
  const [activeMatchModal, setActiveMatchModal] = useState<string | null>(null);
  const [selectedTherapist, setSelectedTherapist] = useState<TherapistDoc | null>(null);
  const [therapistDocs, setTherapistDocs] = useState<TherapistDoc[]>([]);
  const [therapyDocs, setTherapyDocs] = useState<TherapyDoc[]>([]);
  const [classificationCompletion, setClassificationCompletion] = useState(0);

  // Function to fetch therapist documents by IDs
  const fetchTherapistDocs = async (therapistIds: string[]) => {
    try {
      const therapists: TherapistDoc[] = [];
      
      for (const id of therapistIds) {
        const docRef = doc(db, 'therapists', id);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const data = docSnap.data();
          therapists.push({
            content: [data.bio || 'No bio available'],
            metadata: {
              id: docSnap.id,
              status: data.status || "unavailable", 
              name: data.name || 'Unknown',
              job_title: data.job_title || 'Therapist',
              image_url: data.image_url || '/profile1-female.png',
              availability: data.availability || 'unavailable',
              hourly_rate: data.hourly_rate || 'Rate not specified',
              ...data
            }
          });
        }
      }
      
      setTherapistDocs(therapists);
    } catch (err: any) {
      console.error('Error fetching therapist documents:', err);
      setError(`Failed to load therapist details: ${err.message}`);
    }
  };

  // Function to fetch therapy documents by IDs
  const fetchTherapyDocs = async (therapyIds: string[]) => {
    try {
      const therapies: TherapyDoc[] = [];
      
      for (const id of therapyIds) {
        const docRef = doc(db, 'therapies', id);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const data = docSnap.data();
          therapies.push({
            content: [{ text: data.description || 'No description available' }],
            metadata: {
              id: docSnap.id,
              name: data.name || 'Unknown Therapy',
              ...data
            }
          });
        }
      }
      
      setTherapyDocs(therapies);
    } catch (err: any) {
      console.error('Error fetching therapy documents:', err);
      setError(`Failed to load therapy details: ${err.message}`);
    }
  };

  const calculateCompletionPercentage = () => {
    if (!result || !result.request) return 0;
    
    // Define the total number of possible assessment fields
    const totalFields = 10; // moodScore, concerns, note, preferences (gender, price, hours, availability)
    let completedFields = 0;
    
    // Check which fields have data
    if (result.request.moodScore !== undefined) completedFields +2;
    if (result.request.concerns && result.request.concerns.length > 0) completedFields +2;
    if (result.request.note && result.request.note.trim() !== '') completedFields == completedFields +2;
    
    // Check preferences
    if (result.request.preferences) {
      if (result.request.preferences.works_with_gender !== undefined) completedFields++;
      if (result.request.preferences.priceRange !== undefined) completedFields++;
      if (result.request.preferences.workingHours !== undefined) completedFields++;
      if (result.request.preferences.availability !== undefined) completedFields++;
    }
    
    // Calculate percentage
    return Math.round((completedFields / totalFields) * 100);
  };

  useEffect(() => {
    const fetchResult = async () => {
      if (!id) {
        setError('No result ID provided');
        setLoading(false);
        return;
      }

      if (!currentUser) {
        navigate('/login');
        return;
      }

      try {
        const resultRef = doc(db, 'results', id);
        const resultSnap = await getDoc(resultRef);

        if (resultSnap.exists()) {
          const resultData = resultSnap.data() as AssessmentResultRecord;
          setResult(resultData);
          
          // Fetch therapist and therapy documents
          if (resultData.results.therapists && resultData.results.therapists.length > 0) {
            await fetchTherapistDocs(resultData.results.therapists);
          }
          
          if (resultData.results.therapies && resultData.results.therapies.length > 0) {
            await fetchTherapyDocs(resultData.results.therapies);
          }
        } else {
          setError('Result not found for ' + id);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load result: ' + err);
      } finally {
        setLoading(false);
      }
    };

    fetchResult();
  }, [id, currentUser, navigate]);

  if (loading) {
    return (
      <div className="max-w-1000 mx-auto p-4">
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
          Loading results...
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="max-w-1000 mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          {error || 'Failed to load result'}
        </div>
      </div>
    );
  }

  return (
    <div className="w-[80%] sm:w-[80%] mx-auto p-4">
      <div className="mb-4 text-gray-800 p-2">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6">
          ⚠ If you feel unwell or you are at <b>crisis</b> point, please seek emergency help immediately from your local healthcare provider.
        </div>

        <h2 className='text-3xl font-bold mb-4 text-gray-700'>
          Your Results
          <a href="/demo" className='float-right inline-flex center-text items-center px-4 m-auto py-2 text-sm font-medium text-center border-1 border-gray-400 rounded-lg text-gray-100 bg-gray-400 shadow-xl border-solid font-medium text-gray-800 hover:text-gray-900 hover:bg-teal-600'>
            Take New Assessment
          </a>
        </h2>
      </div>

      {/* Results display */}
      <div className="bg-white rounded-lg shadow-md p-6">
        {/* Tab navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex flex-wrap sm:space-x-8 space-x-2">
            <button
              onClick={() => setActiveTab('therapies')}
              className={`${
                activeTab === 'therapies'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } py-4 px-1 border-b-2 font-medium text-sm flex-1 sm:flex-none text-center sm:text-left`}
            >
              <span className="hidden sm:inline">🧠 Recommended Therapies ({result.results?.therapies?.length})</span>
              <span className="sm:hidden">🧠 Therapies</span>
            </button>
            <button
              onClick={() => setActiveTab('professionals')}
              className={`${
                activeTab === 'professionals'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } py-4 px-1 border-b-2 font-medium text-sm flex-1 sm:flex-none text-center sm:text-left`}
            >
              <span className="hidden sm:inline">🧑‍⚕️ Recommended Professionals ({result.results?.therapists?.length})</span>
              <span className="sm:hidden">🧑‍⚕️ Therapists</span>
            </button>
            <button
              onClick={() => setActiveTab('assessment')}
              className={`${
                activeTab === 'assessment'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } py-4 px-1 border-b-2 font-medium text-sm flex-1 sm:flex-none text-center sm:text-left`}
            >
              <span className="hidden sm:inline">📋 Assessment Request ({calculateCompletionPercentage()}% complete)</span>
              <span className="sm:hidden">📋 Assessment</span>
            </button>
            <button
              onClick={() => setActiveTab('classification')}
              className={`${
                activeTab === 'classification'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } py-4 px-1 border-b-2 font-medium text-sm flex-1 sm:flex-none text-center sm:text-left`}
            >
              <span className="hidden sm:inline">📊 Condition Classification ({classificationCompletion}%)</span>
              <span className="sm:hidden">📊 Condition</span>
            </button>
          </nav>
        </div>

        {/* Tab content */}
        <div className="py-4">
          {/* Recommended Therapies Tab */}
          {activeTab === 'therapies' && (
            <div className="mt-4 space-y-4">
              <div className='grid bg-gray-200 p-8 rounded-lg mt-4'>
                <div className="prose lg:prose-xl">
                  <ShowMore lines={6}>
                    <Markdown>{result.results.aiTherapyRecommendations}</Markdown>
                  </ShowMore>
                </div>
              </div>
              
              {/* Therapy cards */}
              <div className="lg:grid lg:grid-cols-12 gap-3">
                {therapyDocs.map((therapy, index) => (
                  <div key={index} className="lg:col-span-4 bg-gray-100 border border-gray-200 rounded-lg shadow-sm">
                    <div className="flex flex-col items-center">
                      <h5 className="text-xl h-16 p-4 mt-2 font-medium text-gray-900">{therapy.metadata?.name}</h5>
                      <p className='p-4 h-40'>{therapy.content[0].text.split('.')[0] + "..."}</p>
                      <div className="mt-auto mb-4">
                        <a 
                          href={`/therapies/${therapy.metadata?.id}`}
                          target="_blank"
                          className="inline-flex items-center px-4 py-2 text-sm font-medium text-center border border-gray-300 rounded-lg bg-teal-300 hover:bg-teal-500 text-gray-700"
                        >
                          Read More
                        </a>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recommended Professionals Tab */}
          {activeTab === 'professionals' && (
            <div className="mt-4 space-y-4">
              <div className='grid bg-gray-200 p-8 rounded-lg mt-4'>
                <div className="prose lg:prose-xl">
                  <ShowMore lines={6}>
                    <Markdown>{result.results.aiRecommendations}</Markdown>
                  </ShowMore>
                </div>
              </div>
              
              {/* Therapist cards */}
              <div className="lg:grid lg:grid-cols-12 gap-3">
                {therapistDocs.map((doc, index) => (
                  <div key={index} className="lg:col-span-4 bg-gray-100 border border-gray-200 rounded-lg shadow-sm">
                    <div className="flex flex-col items-center pb-10">
                      <a className='' target='_blank' href={`/therapists/profile/${doc.metadata?.id}`}>
                        <img className="h-32 w-32 object-contain mb-3 rounded-full shadow-lg" src={doc.metadata?.image_url || '/profile1-female.png'} alt="Therapist"/>
                      </a>
                      <h5 className="mb-1 text-xl font-medium text-gray-900">{doc.metadata?.name}</h5>
                      <span className="text-m text-gray-900 dark:text-gray-400 px-4">{doc.metadata?.job_title}</span>
                      <span className="text-m text-gray-900 dark:text-gray-400 px-4 font-bold">{doc.metadata?.availability === "available" ? "Available Now" : "Not Currently Available"}</span>
                      <span className="text-m text-gray-900 dark:text-gray-400 px-4 font-bold">{doc.metadata?.hourly_rate}</span>
                      <div className="flex mt-4 md:mt-6">
                        <button 
                          onClick={() => {
                            setActiveMatchModal(doc.metadata?.id || null);
                            setSelectedTherapist(doc);
                          }}
                          className="inline-flex items-center px-4 py-2 text-sm font-medium text-center border-1 border-gray-400 rounded-lg text-gray-100 bg-teal-400 shadow-xl border-solid font-medium text-gray-800 hover:text-gray-900 hover:bg-teal-600"
                        >
                          Send A Match Request
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Assessment Request Tab */}
          {activeTab === 'assessment' && (
            <div className="mt-4 space-y-4">
              <div className='grid bg-gray-200 p-8 rounded-lg mt-4'>
                <div className="prose lg:prose-xl">
                  <h3 className="text-xl font-semibold mb-4">Your Assessment Summary</h3>
                  
                  {/* Mood Score */}
                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-800">Mood Score</h4>
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      <div className="flex items-center">
                        <div className="w-16 h-16 rounded-full bg-teal-100 flex items-center justify-center text-2xl font-bold text-teal-700">
                          {result.request.moodScore}
                        </div>
                        <div className="ml-4">
                          <p className="text-gray-700">
                            {result.request.moodScore <= 3 ? 'Your mood score indicates you may be experiencing significant distress.' : 
                             result.request.moodScore <= 6 ? 'Your mood score indicates moderate emotional challenges.' : 
                             'Your mood score indicates relatively positive emotional wellbeing.'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Concerns */}
                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-800">Your Concerns</h4>
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                      {result.request.concerns && result.request.concerns.length > 0 ? (
                        <ul className="list-disc pl-5 space-y-1">
                          {result.request.concerns.map((concern, index) => (
                            <li key={index} className="text-gray-700 capitalize">{concern}</li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-gray-700">No specific concerns were identified.</p>
                      )}
                    </div>
                  </div>
                  
                  {/* Notes */}
                  {result.request.note ? (
                    <div className="mb-6">
                      <h4 className="text-lg font-medium text-gray-800">Your Notes</h4>
                      <div className="bg-white p-4 rounded-lg shadow-sm">
                        <p className="text-gray-700">{result.request.note}</p>
                      </div>
                    </div>
                  ) : (
                    <div className="mb-6">
                      <h4 className="text-lg font-medium text-gray-800">Your Notes</h4>
                      <div className="bg-white p-4 rounded-lg shadow-sm">
                        <p className="text-gray-700">No additional notes provided.</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Preferences */}
                  {result.request.preferences && Object.keys(result.request.preferences).length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-lg font-medium text-gray-800">Your Preferences</h4>
                      <div className="bg-white p-4 rounded-lg shadow-sm">
                        <ul className="list-disc pl-5 space-y-1">
                          {Object.entries(result.request.preferences).map(([key, value]) => (
                            <li key={key} className="text-gray-700">
                              <span className="font-medium">{key.replace(/_/g, ' ')}:</span> {String(value)}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                  
                  {/* Take New Assessment Button */}
                  <div className="mt-8">
                    <a 
                      href="/demo" 
                      className="inline-flex items-center px-4 py-2 text-sm font-medium text-center border border-gray-400 rounded-lg bg-teal-400 shadow-xl text-gray-800 hover:text-gray-900 hover:bg-teal-600"
                    >
                      Re-Take Assessment
                    </a>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Condition Classification Tab */}
          {activeTab === 'classification' && (
            <div className="mt-4 space-y-4">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-4">Indicative Condition Classification</h3>
                <p className="text-gray-600 mb-6">
                  This assessment helps identify potential mental health conditions based on standardized screening tools.
                  Your responses are confidential and will help provide more targeted recommendations.
                </p>
                
                <ConditionClassification 
                  resultId={id}
                  onCompletionChange={(percentage) => setClassificationCompletion(percentage)}
                />
                
                <div className="mt-6 p-4 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700">
                  <p className="font-medium">Important Note:</p>
                  <p>This is not a diagnostic tool. Results should be discussed with a qualified healthcare professional.</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Matching Modal */}
      {activeMatchModal && selectedTherapist && (
        <MatchingModal
          isOpen={!!activeMatchModal}
          setIsOpen={(open) => {
            if (!open) setActiveMatchModal(null);
          }}
          therapistId={activeMatchModal}
          therapistName={selectedTherapist.metadata?.name || 'Therapist'}
        />
      )}
    </div>
  );
};

export default Result;
