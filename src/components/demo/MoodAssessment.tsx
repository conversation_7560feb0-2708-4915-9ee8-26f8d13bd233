import { useState, FormEvent, useEffect } from 'react';
import { submitMoodAssessment } from '../../utils/firebaseFunctions';
import { analytics } from '../Firebase';
import { logEvent } from "firebase/analytics";
import MoodAssessmentForm from "./MoodAssessmentForm";
import { useAuth } from '../contexts/AuthContext';

interface AiAssessmentResponse {
  aiRecommendations?: string;
  aiTherapyRecommendations?: string;
  aiDocs?: TherapistDoc[];
  therapies?: TherapyDoc[];
  resultId: string;
}

export interface TherapyDoc {
  content: [{text: string}];
  metadata: {
    name: string;
    id: string
  }
}

export interface TherapistDoc {
    content: string[];
    metadata: {
        id: string;
        image_url: string;
        job_title: string;
        name: string;
        status: string;
        availability: string;
        hourly_rate: number;
    };
}

export interface Preferences {
    therapistGender: 'no_preference' | 'male' | 'female',
    sessionType: 'in_person' | 'online' | 'no_preference',
    priceRange: {
        min: number;
        max: number;
    },
    location?: string,
    workingHours?: '9-5' | 'flexible',
    availability: boolean;
    works_with_gender: 'no_preference' | 'male' | 'female' | 'non-binary' | 'prefer-not-to-say';
    organisation_id?: string; 
}

export interface Profile {
    dateOfBirth?: string;
    gender?: string;
    userLocation?: string;
};

interface MoodsPropTypes {
  embedded?: boolean
  organisationId?: string;
}
  
const MoodAssessment = ({embedded, organisationId}: MoodsPropTypes) => {
  const { currentUser, signInAnonymously } = useAuth();
  const [moodScore, setMoodScore] = useState('5');
  // const [note, setNote] = useState("I have been feeling lower and lower the past week or so, sleep isn't too great due to the usual bad habits, work is frustrating and worried about running low on money. Dating isn't going very well either, probably due to the above.");
  const [note, setNote] = useState("");
  const [concerns, setConcerns] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [preferences, setPreferences] = useState<Preferences | null>({
    therapistGender: 'no_preference',
    sessionType: 'no_preference',
    works_with_gender: 'no_preference',
    priceRange: {
        min: 0,
        max: 50
    },
    workingHours: 'flexible',
    availability: true
  });

  const [profile, setProfile] = useState<Profile>({
    dateOfBirth: undefined,
    gender: undefined,
    userLocation: undefined,
  });

  const [aiAssessment] = useState<AiAssessmentResponse>({
    aiRecommendations: "",
    aiTherapyRecommendations: "",
    aiDocs: [],
    therapies: [],
    resultId: ""
  });

  // Ensure anonymous authentication for Firebase UID persistence
  useEffect(() => {
    const ensureAnonymousAuth = async () => {
      if (!currentUser) {
        try {
          console.log("No current user, signing in anonymously...");
          await signInAnonymously();
        } catch (error) {
          console.error("Failed to sign in anonymously:", error);
        }
      } else {
        console.log("Current user exists:", currentUser.uid);
      }
    };

    ensureAnonymousAuth();
  }, [currentUser, signInAnonymously]);

  const handleSubmit =  async (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    // Ensure we have a user (anonymous or authenticated) before submitting
    if (!currentUser) {
      try {
        console.log("No user found during submission, signing in anonymously...");
        await signInAnonymously();
      } catch (error) {
        console.error("Failed to authenticate before submission:", error);
        setError("Authentication failed. Please try again.");
        setIsSubmitting(false);
        return;
      }
    }

    console.log({
      embedded: embedded,
      organisationId: organisationId,
      preferences: preferences,
    });

    if (embedded && organisationId && preferences) {
      console.log(" XXX: ", organisationId);
      preferences.organisation_id = organisationId;
    } else {
      console.log(" XXX: ", organisationId)
    };

    console.log("XXX submitMoodAssessment: ", {
      moodScore: parseInt(moodScore),
      concerns,
      note,
      preferences,
      profile
    });

    logEvent(analytics, "mood_assessment", { 
      content_type: "submission",
      moodScore: parseInt(moodScore),
      concerns,
      note,
      preferences,
      profile
    });

    // Handle form submission here

    try {
        const newAiAssessment: AiAssessmentResponse = await submitMoodAssessment({
          moodScore: parseInt(moodScore),
          concerns,
          note,
          preferences,
          profile
        });

        console.log("newAiAssessment: ", newAiAssessment);

        aiAssessment.aiDocs = newAiAssessment.aiDocs;
        aiAssessment.aiRecommendations = newAiAssessment.aiRecommendations;
        aiAssessment.aiTherapyRecommendations = newAiAssessment.aiTherapyRecommendations;
        aiAssessment.therapies = newAiAssessment.therapies;
        aiAssessment.resultId = newAiAssessment.resultId;
        
        console.log("aiAssessment: ", aiAssessment);

        if(newAiAssessment.resultId) {
          window.location.href = `/demo/result/${newAiAssessment.resultId}`;
        };

        // Reset form on success
        // setMoodScore('5');
        // setConcerns([]);
        // setSummary('');
        
      } catch (err: any) {
        setError("Error: " + err.message || 'Failed to submit assessment');
      } finally {
        setIsSubmitting(false);

        logEvent(analytics, "assessment_response", { 
          content_type: "aiAssessment",
          aiAssessment
        });

        // Reset form fields
        setMoodScore('5');
        setConcerns([]);
        setNote('');
      }

  };

  return (
    <div className="w-[80%] sm:w-[80%] mx-auto p-4 mx-auto p-4">
      <div className=" mb-4 text-gray-800 p-2">

      {!isSubmitting && aiAssessment.aiRecommendations && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6">
          If you feel unwell or your are at <b>crisis</b> point, please seek emergency help immediately from your local healthcare provider.
        </div>
      )}

      { !embedded && !isSubmitting && (
        <h2 className='text-3xl font-bold mb-4 text-gray-700'>
          {aiAssessment.aiRecommendations ? "Your Results..." : "Find the right therapy and therapist..." }

          {aiAssessment.aiRecommendations ?
            <a href="/demo" className='float-right inline-flex center-text items-center px-4 m-auto py-2 text-sm font-medium text-center border-1 border-gray-400 rounded-lg text-gray-100 bg-gray-400 shadow-xl border-solid font-medium text-gray-800 hover:text-gray-900 hover:bg-teal-600'>
            Reset & Refresh</a>
          : ""}
        </h2>
      )}

      {aiAssessment.aiRecommendations || isSubmitting ? "" :
        <p className='text-lg text-gray-600 pt-2'>
          Neuro helps you find the right kind of therapy for you, 
          and then matches you with an appropriate therapist.
          <br/>
          We use AI and don't ask for any personally identifiable information, all data is shared or kept anonymously.
          
        </p>
      }
      </div>

      <div>
      { aiAssessment.aiRecommendations && embedded ?
        <a href={`/demo/embed?organisation_id=${organisationId}`} className='float-right inline-flex center-text items-center px-4 m-auto py-2 text-sm font-medium text-center border-1 border-gray-400 rounded-lg text-gray-100 bg-gray-400 shadow-xl border-solid font-medium text-gray-800 hover:text-gray-900 hover:bg-teal-600'>
        Reset & Refresh</a>
      : ""}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          {error}
        </div>
      )}

      {isSubmitting && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
        Finding therapies and therapists ...
        </div>
      )}

      {!aiAssessment.aiRecommendations && !isSubmitting ? (
        <MoodAssessmentForm
          onSubmit={handleSubmit}
          moodScore={moodScore}
          setMoodScore={setMoodScore}
          concerns={concerns}
          setConcerns={setConcerns}
          note={note}
          setNote={setNote}
          isSubmitting={isSubmitting}
          preferences={preferences}
          setPreferences={setPreferences}
          profile={profile}
          setProfile={setProfile}
        />
      ) : (
        <div>
        </div>
      )}
    </div>
  );
};

export default MoodAssessment;
