import { useState, FormEvent, useEffect } from 'react';
import { submitMoodAssessment } from '../../utils/firebaseFunctions';
import { analytics } from '../Firebase';
import { logEvent } from "firebase/analytics";
import MoodAssessmentForm from "./MoodAssessmentForm";
import { useAuth } from '../contexts/AuthContext';
import {
  saveAssessmentData,
  getStoredAssessmentData,
  clearStoredAssessmentData,
  hasStoredAssessmentData,
  saveResultData
} from '../../utils/assessmentStorage';

interface AiAssessmentResponse {
  aiRecommendations?: string;
  aiTherapyRecommendations?: string;
  aiDocs?: TherapistDoc[];
  therapies?: TherapyDoc[];
  resultId: string;
}

export interface TherapyDoc {
  content: [{text: string}];
  metadata: {
    name: string;
    id: string
  }
}

export interface TherapistDoc {
    content: string[];
    metadata: {
        id: string;
        image_url: string;
        job_title: string;
        name: string;
        status: string;
        availability: string;
        hourly_rate: number;
    };
}

export interface Preferences {
    therapistGender: 'no_preference' | 'male' | 'female',
    sessionType: 'in_person' | 'online' | 'no_preference',
    priceRange: {
        min: number;
        max: number;
    },
    location?: string,
    workingHours?: '9-5' | 'flexible',
    availability: boolean;
    works_with_gender: 'no_preference' | 'male' | 'female' | 'non-binary' | 'prefer-not-to-say';
    organisation_id?: string; 
}

export interface Profile {
    dateOfBirth?: string;
    gender?: string;
    userLocation?: string;
};

interface MoodsPropTypes {
  embedded?: boolean
  organisationId?: string;
  clearStoredData?: boolean;
}
  
const MoodAssessment = ({embedded, organisationId, clearStoredData}: MoodsPropTypes) => {
  const { currentUser, signInAnonymously } = useAuth();

  // Check URL parameters for clear flag
  const urlParams = new URLSearchParams(window.location.search);
  const shouldClearData = clearStoredData || urlParams.get('clear') === 'true';

  // Initialize state with stored data or defaults
  const initializeState = () => {
    if (shouldClearData) {
      clearStoredAssessmentData();
      return {
        moodScore: '5',
        note: '',
        concerns: [],
        preferences: {
          therapistGender: 'no_preference' as const,
          sessionType: 'no_preference' as const,
          works_with_gender: 'no_preference' as const,
          priceRange: { min: 0, max: 50 },
          workingHours: 'flexible' as const,
          availability: true
        },
        profile: {
          dateOfBirth: undefined,
          gender: undefined,
          userLocation: undefined,
        }
      };
    }

    const storedData = getStoredAssessmentData();
    return {
      moodScore: storedData.moodScore,
      note: storedData.note,
      concerns: storedData.concerns,
      preferences: storedData.preferences,
      profile: storedData.profile
    };
  };

  const initialState = initializeState();

  const [moodScore, setMoodScoreState] = useState(initialState.moodScore);
  const [note, setNoteState] = useState(initialState.note);
  const [concerns, setConcernsState] = useState<string[]>(initialState.concerns);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [preferences, setPreferencesState] = useState<Preferences | null>(initialState.preferences);
  const [profile, setProfileState] = useState<Profile>(initialState.profile);

  // Progressive saving wrapper functions
  const setMoodScore = (value: string) => {
    setMoodScoreState(value);
    saveAssessmentData({ moodScore: value });
  };

  const setNote = (value: string) => {
    setNoteState(value);
    saveAssessmentData({ note: value });
  };

  const setConcerns = (value: string[]) => {
    setConcernsState(value);
    saveAssessmentData({ concerns: value });
  };

  const setPreferences = (value: Preferences) => {
    setPreferencesState(value);
    saveAssessmentData({ preferences: value });
  };

  const setProfile = (value: Profile) => {
    setProfileState(value);
    saveAssessmentData({ profile: value });
  };

  const [aiAssessment] = useState<AiAssessmentResponse>({
    aiRecommendations: "",
    aiTherapyRecommendations: "",
    aiDocs: [],
    therapies: [],
    resultId: ""
  });

  // Clear URL parameters after processing
  useEffect(() => {
    if (shouldClearData && urlParams.get('clear') === 'true') {
      // Remove the clear parameter from URL without page reload
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('clear');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [shouldClearData, urlParams]);

  // Ensure anonymous authentication for Firebase UID persistence
  useEffect(() => {
    const ensureAnonymousAuth = async () => {
      if (!currentUser) {
        try {
          console.log("No current user, signing in anonymously...");
          await signInAnonymously();
        } catch (error) {
          console.error("Failed to sign in anonymously:", error);
        }
      } else {
        console.log("Current user exists:", currentUser.uid);
      }
    };

    ensureAnonymousAuth();
  }, [currentUser, signInAnonymously]);

  const handleSubmit =  async (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    // Ensure we have a user (anonymous or authenticated) before submitting
    if (!currentUser) {
      try {
        console.log("No user found during submission, signing in anonymously...");
        await signInAnonymously();
      } catch (error) {
        console.error("Failed to authenticate before submission:", error);
        setError("Authentication failed. Please try again.");
        setIsSubmitting(false);
        return;
      }
    }

    console.log({
      embedded: embedded,
      organisationId: organisationId,
      preferences: preferences,
    });

    if (embedded && organisationId && preferences) {
      console.log(" XXX: ", organisationId);
      preferences.organisation_id = organisationId;
    } else {
      console.log(" XXX: ", organisationId)
    };

    console.log("XXX submitMoodAssessment: ", {
      moodScore: parseInt(moodScore),
      concerns,
      note,
      preferences,
      profile
    });

    logEvent(analytics, "mood_assessment", { 
      content_type: "submission",
      moodScore: parseInt(moodScore),
      concerns,
      note,
      preferences,
      profile
    });

    // Handle form submission here

    try {
        const newAiAssessment: AiAssessmentResponse = await submitMoodAssessment({
          moodScore: parseInt(moodScore),
          concerns,
          note,
          preferences,
          profile
        });

        console.log("newAiAssessment: ", newAiAssessment);

        aiAssessment.aiDocs = newAiAssessment.aiDocs;
        aiAssessment.aiRecommendations = newAiAssessment.aiRecommendations;
        aiAssessment.aiTherapyRecommendations = newAiAssessment.aiTherapyRecommendations;
        aiAssessment.therapies = newAiAssessment.therapies;
        aiAssessment.resultId = newAiAssessment.resultId;
        
        console.log("aiAssessment: ", aiAssessment);

        if(newAiAssessment.resultId) {
          // Save the result data to localStorage, overriding the request data
          saveResultData(newAiAssessment.resultId, {
            moodScore,
            note,
            concerns,
            preferences,
            profile
          });

          window.location.href = `/result/${newAiAssessment.resultId}`;
        };

        // Reset form on success
        // setMoodScore('5');
        // setConcerns([]);
        // setSummary('');
        
      } catch (err: any) {
        setError("Error: " + err.message || 'Failed to submit assessment');
      } finally {
        setIsSubmitting(false);

        logEvent(analytics, "assessment_response", { 
          content_type: "aiAssessment",
          aiAssessment
        });

        // Reset form fields
        setMoodScore('5');
        setConcerns([]);
        setNote('');
      }

  };

  return (
    <div className="w-[80%] sm:w-[80%] mx-auto p-4 mx-auto p-4">
      <div className=" mb-4 text-gray-800 p-2">

      {!isSubmitting && aiAssessment.aiRecommendations && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6">
          If you feel unwell or your are at <b>crisis</b> point, please seek emergency help immediately from your local healthcare provider.
        </div>
      )}

      { !embedded && !isSubmitting && (
        <h2 className='text-3xl font-bold mb-4 text-gray-700'>
          {aiAssessment.aiRecommendations ? "Your Results..." : "Find the right therapy and therapist..." }

          {aiAssessment.aiRecommendations ?
            <a href="/" className='float-right inline-flex center-text items-center px-4 m-auto py-2 text-sm font-medium text-center border-1 border-gray-400 rounded-lg text-gray-100 bg-gray-400 shadow-xl border-solid font-medium text-gray-800 hover:text-gray-900 hover:bg-teal-600'>
            Reset & Refresh</a>
          : ""}
        </h2>
      )}

      {aiAssessment.aiRecommendations || isSubmitting ? "" :
        <p className='text-lg text-gray-600 pt-2'>
          Neuro helps you find the right kind of therapy for you, 
          and then matches you with an appropriate therapist.
          <br/>
          We use AI and don't ask for any personally identifiable information, all data is shared or kept anonymously.
          
        </p>
      }
      </div>

      <div>
      { aiAssessment.aiRecommendations && embedded ?
        <a href={`/demo/embed?organisation_id=${organisationId}`} className='float-right inline-flex center-text items-center px-4 m-auto py-2 text-sm font-medium text-center border-1 border-gray-400 rounded-lg text-gray-100 bg-gray-400 shadow-xl border-solid font-medium text-gray-800 hover:text-gray-900 hover:bg-teal-600'>
        Reset & Refresh</a>
      : ""}
      </div>

      {/* Stored data indicator */}
      {!shouldClearData && hasStoredAssessmentData() && !aiAssessment.aiRecommendations && !isSubmitting && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-4">
          <div className="flex justify-between items-center">
            <span>📝 You have saved assessment data. Continue where you left off or start fresh.</span>
            <a
              href={embedded ? `/demo/embed?organisation_id=${organisationId}&clear=true` : "/?clear=true"}
              className="inline-flex items-center px-3 py-1 text-sm font-medium text-center border border-blue-600 rounded-lg bg-blue-200 hover:bg-blue-300 text-blue-800"
            >
              Start New Assessment
            </a>
          </div>
        </div>
      )}

       {/* Safety indicator */}
      {!hasStoredAssessmentData() && !aiAssessment.aiRecommendations && !isSubmitting && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-4">
          <div className="flex justify-between items-center">
            <span>⚠ If you feel unwell or you are at <b>crisis</b> point, please seek emergency help immediately from your local healthcare provider.</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          {error}
        </div>
      )}

      {isSubmitting && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
        ⌛ Finding therapies and therapists ...
        </div>
      )}

      {!aiAssessment.aiRecommendations && !isSubmitting ? (
        <MoodAssessmentForm
          onSubmit={handleSubmit}
          moodScore={moodScore}
          setMoodScore={setMoodScore}
          concerns={concerns}
          setConcerns={setConcerns}
          note={note}
          setNote={setNote}
          isSubmitting={isSubmitting}
          preferences={preferences}
          setPreferences={setPreferences}
          profile={profile}
          setProfile={setProfile}
        />
      ) : (
        <div>
        </div>
      )}
    </div>
  );
};

export default MoodAssessment;
