import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from "react";
import { Preferences, Profile } from "./MoodAssessment";
import { useState } from "react";

interface FormPropTypes {
  onSubmit: FormEventHandler;
  moodScore: string;
  setMoodScore: (input: string) => void;
  concerns: string[];
  setConcerns: (concerns: string[]) => void;
  note: string;
  setNote: (note: string) => void;
  preferences: Preferences | null;
  setPreferences: (preferences: Preferences) => void;
  isSubmitting: boolean;
  profile: Profile;
  setProfile: (profile: Profile) => void;
}

const CONCERN_OPTIONS = [
  'sleep',
  'relationships',
  'finances',
  'education',
  'work life',
  'social life',
  'family life',
  'physical health'
] as const;

const MoodAssessmentForm = ({ 
  onSubmit, 
  moodScore, 
  setMoodScore,
  concerns,
  setConcerns,
  note,
  setNote,
  preferences,
  setPreferences,
  profile,
  setProfile,
  isSubmitting
}: FormPropTypes) => {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;
  
  const handleConcernToggle = (concern: string) => {
    if (concerns.includes(concern)) {
      setConcerns(concerns.filter(c => c !== concern));
    } else {
      setConcerns([...concerns, concern]);
    }
  };

  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));
  
  return (
    <form onSubmit={onSubmit} className="bg-white p-8 rounded-lg mt-4 shadow-md">
      {/* Progress indicator */}
      <div className="mb-8">
        <div className="flex justify-between mb-2">
          {Array.from({length: totalSteps}).map((_, idx) => (
            <div 
              key={idx} 
              className={`flex items-center justify-center w-10 h-10 rounded-full 
                ${currentStep > idx ? 'bg-teal-500 text-white' : 
                  currentStep === idx + 1 ? 'bg-teal-200 text-gray-800 border-2 border-teal-500' : 
                  'bg-gray-200 text-gray-500'}`}
            >
              {idx + 1}
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 h-2 rounded-full">
          <div 
            className="bg-teal-500 h-2 rounded-full transition-all duration-300" 
            style={{width: `${(currentStep / totalSteps) * 100}%`}}
          ></div>
        </div>
      </div>

      {/* Step 1: Basic Mood Assessment */}
      {currentStep === 1 && (
        <div className="space-y-8">
          <h2 className="text-2xl font-semibold text-gray-800">How are you feeling?</h2>
          
          <div className="bg-gray-50 p-6 rounded-lg">
            <label htmlFor="mood" className="block text-lg font-medium text-gray-700 mb-3">
              How are you feeling generally, out of 10?
              <span className="float-right ml-2 text-2xl font-bold text-teal-600">{moodScore}/10</span>
            </label>
            <input
              onChange={(e) => setMoodScore(e.target.value)}
              name="mood"
              value={moodScore}
              className="w-full h-4 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-teal-500"
              type="range"
              min="1"
              max="10"
              required
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>Not great</span>
              <span>Neutral</span>
              <span>Excellent</span>
            </div>
          </div>
          
          <div className="bg-gray-50 p-6 rounded-lg">
            <label className="block text-lg font-medium text-gray-700 mb-3">
              Do you have any particular areas of concern?
            </label>
            <div className="flex flex-wrap gap-2">
              {CONCERN_OPTIONS.map((option) => (
                <button
                  key={option}
                  type="button"
                  onClick={() => handleConcernToggle(option)}
                  className={`py-2 px-4 rounded-full border-2 text-base capitalize transition-all duration-200 
                    ${concerns.includes(option) 
                      ? 'bg-teal-100 text-teal-800 border-2 border-teal-500' 
                      : 'bg-white text-gray-700 border-2 border-gray-300 hover:bg-gray-100'
                    }`}
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
          
          <div className="flex justify-end">
            
            <button 
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-3 bg-teal-500 text-white rounded-lg hover:bg-teal-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting...' : 'Find Support Now'}
            </button>
            <button
              type="button"
              onClick={nextStep}
              className="px-6 ml-3 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Continue
            </button>
          </div>
        </div>
      )}

      {/* Step 2: Additional Context */}
      {currentStep === 2 && (
        <div className="space-y-8">
          <h2 className="text-2xl font-semibold text-gray-800">Tell us more ...</h2>
          
          <div className="bg-gray-50 p-6 rounded-lg">
            <label htmlFor="note" className="block text-lg font-medium text-gray-700 mb-3">
              Is there anything else you'd like to add about how you are feeling? (optional)
            </label>
            <textarea
              onChange={(e) => setNote(e.target.value)}
              name="note"
              value={note}
              className="w-full p-3 text-gray-700 bg-white rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent" 
              placeholder="Share any additional troubling thoughts or things you are concerned about here..."
              rows={4}
            ></textarea>
          </div>
          
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Your Profile Information (optional)</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                <input
                  type="date"
                  value={profile?.dateOfBirth || ''}
                  onChange={(e) => setProfile({
                    ...profile,
                    dateOfBirth: e.target.value
                  })}
                  className="w-full p-2 text-gray-700 bg-white rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                <select
                  value={profile?.gender || ''}
                  onChange={(e) => setProfile({
                    ...profile,
                    gender: e.target.value
                  })}
                  className="w-full p-2 text-gray-700 bg-white rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="non-binary">Non-binary</option>
                  <option value="prefer-not-to-say">Prefer not to say</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <input
                  type="text"
                  value={profile?.userLocation || ''}
                  onChange={(e) => setProfile({
                    ...profile,
                    userLocation: e.target.value
                  })}
                  placeholder="Enter your city"
                  className="w-full p-2 text-gray-700 bg-white rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
          
          <div className="flex justify-between">
            <div>
                <button
                  type="button"
                  onClick={prevStep}
                  className="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Back
                </button>
            </div>
            <div>
              <button 
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-3 bg-teal-500 text-white rounded-lg hover:bg-teal-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Submitting...' : 'Find Support Now'}
              </button>
              <button
                type="button"
                onClick={nextStep}
                className="px-6 ml-3 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                Continue
              </button>
            </div>  
          </div>
        </div>
      )}

      {/* Step 3: Preferences */}
      {currentStep === 3 && (
        <div className="space-y-8">
          <h2 className="text-2xl font-semibold text-gray-800">Your Preferences (optional)</h2>
          
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Help us find the right support for you</h3>
            
            <div className="space-y-6">
              <div>
                <div className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    id="availability"
                    checked={preferences?.availability || false}
                    onChange={(e) => setPreferences({
                      ...preferences!,
                      availability: e.target.checked
                    })}
                    className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                  />
                  <label htmlFor="availability" className="ml-2 text-base font-medium text-gray-700">
                    I need someone available now
                  </label>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-base font-medium text-gray-700 mb-2">
                    Preferred therapist gender
                  </label>
                  <select
                    value={preferences?.therapistGender || 'no_preference'}
                    onChange={(e) => setPreferences({
                      ...preferences!,
                      therapistGender: e.target.value as 'no_preference' | 'male' | 'female'
                    })}
                    className="w-full p-2 text-gray-700 bg-white rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  >
                    <option value="no_preference">No Preference</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-base font-medium text-gray-700 mb-2">
                    Session type
                  </label>
                  <select
                    value={preferences?.sessionType || 'no_preference'}
                    onChange={(e) => setPreferences({
                      ...preferences!,
                      sessionType: e.target.value as 'in_person' | 'online' | 'no_preference'
                    })}
                    className="w-full p-2 text-gray-700 bg-white rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  >
                    <option value="no_preference">No Preference</option>
                    <option value="in_person">In Person</option>
                    <option value="online">Online</option>
                  </select>
                </div>
              </div>
              
              {preferences?.sessionType === "in_person" && (
                <div>
                  <label className="block text-base font-medium text-gray-700 mb-2">
                    Your location (for in-person sessions)
                  </label>
                  <input
                    type="text"
                    value={preferences?.location || ''}
                    onChange={(e) => setPreferences({
                      ...preferences!,
                      location: e.target.value
                    })}
                    placeholder="Enter your city"
                    className="w-full p-2 text-gray-700 bg-white rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                </div>
              )}
              
              <div>
                <label className="block text-base font-medium text-gray-700 mb-2">
                  Preferred working hours
                </label>
                <select
                  value={preferences?.workingHours || 'flexible'}
                  onChange={(e) => setPreferences({
                    ...preferences!,
                    workingHours: e.target.value as '9-5' | 'flexible'
                  })}
                  className="w-full p-2 text-gray-700 bg-white rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="flexible">Flexible</option>
                  <option value="9-5">9-5</option>
                </select>
              </div>
            </div>
          </div>
          
          <div className="flex justify-between">
            <button
              type="button"
              onClick={prevStep}
              className="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Back
            </button>
            <button 
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-3 bg-teal-500 text-white rounded-lg hover:bg-teal-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting...' : 'Find Support'}
            </button>
          </div>
        </div>
      )}
    </form>
  );
};

export default MoodAssessmentForm;
