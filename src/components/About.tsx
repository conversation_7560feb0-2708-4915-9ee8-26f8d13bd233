const About = () => {
    
    const openInNewTab = (url : string) => {
        window.open(url, "_blank", "noreferrer");
    };
  
    return (
      <div className="h-screen w-screen p-4 bg-teal-100 overflow-y-scroll">
          <div className="bg-white max-w-[800px] w-full m-auto rounded-md shadow-xl p-4 mt-4">
            <div className="grid md:grid-flow-col ml-6">
              <div className="col-span-4">
                <img src="/mood-rings-logo-small.png" width="75px" className="object-left float-right" alt="Neuro Logo" />
              </div>
              <div className="col-span-8">
                <h1 className="text-4xl font-bold text-gray-700 pl-4 pt-6 leading-10">
                  Welcome
                </h1>
              </div>
            </div>
  
            <div className="px-8 py-4 text-xl">
  
              <div>
                  <p className="mt-4">We&apos;re on a mission to help improve general mood and wellbeing.</p>
                  <p className="mt-4">We are building tools and services to help users, and their supporters, to better track mental health and wellbeing.</p>
              </div>
              <div>
                  <p className="mt-4">The first tool is a simple sms and web mood tracking service, available for anyone to keep score of how they are feeling about wellbeing factors important to them.</p>
              </div>
              <br/>
              <hr/>
              <div>
                  <p className="mt-4">We have a free online demo (no installs, downloads or sign-ups are required) <br />
                  Feel free to try this out below ...</p>
                  <p className="float-center mt-4" >
                      <button onClick={() => openInNewTab("/")} className="w-full mt-2 p-4 border-2 border-gray-600 rounded-lg text-gray-800 bg-gray-200 text-m shadow-xl border-solid font-sm text-gray-700 hover:text-black-500 hover:bg-gray-300">
                          Take me to the demo!
                      </button>
                  </p>
              </div>
              <br/>
              <hr/>
            </div>
  
          </div>
        </div>
    );
  };
  
  export default About;
  