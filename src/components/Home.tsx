// import { db, analytics } from "./Firebase";

const Home = () => {
    
  const openInNewTab = (url : string) => {
      window.open(url, "_blank", "noreferrer");
  };

  return (
    <div className="h-screen w-screen p-4 bg-teal-100 overflow-y-scroll">
        <div className="bg-white max-w-[1200px] w-full m-auto rounded-md shadow-xl p-4 mt-4">
          <div className="grid md:grid-flow-col ml-6">
            <div className="col-span-4">
              <img src="/mood-rings-logo-small.png" width="75px" className="object-left float-right" alt="MoodRings Logo" />
            </div>
            <div className="col-span-8">
              <h1 className="text-4xl font-bold text-gray-700 pl-4 pt-6 leading-10">
                Welcome
              </h1>
            </div>
          </div>

          <div className="px-8 py-4 text-xl">

            <div>
                <p className="mt-4">We&apos;re on a mission to help improve general mood and wellbeing.</p>
                <p className="mt-4">We are building tools and services to help users, and their supporters, to better track mental health and wellbeing.</p>
            </div>
            <br/>
            <hr/>
            <div>
                <p className="mt-4">We have a free online demo available - no installs, downloads or sign-ups are required - feel free to try it out below ...</p>
                <p className="float-center mt-4" >
                    <button onClick={() => openInNewTab("/")} className="w-full mt-2 p-4 border-2 border-gray-600 rounded-lg text-gray-800 bg-gray-200 text-m shadow-xl border-solid font-sm text-gray-700 hover:text-black-500 hover:bg-gray-300">
                        Try out the free demo!
                    </button>
                </p>
            </div>
            <br/>
            <hr/>
          </div>

          <div className="px-8 py-4 text-xl">
                  <p className="mt-4">We also have free trials available of the first beta version.<br/>Sign up below to create a trial for yourself or invite others to.</p>
          </div>

          <div className="px-8 ">

              <div className="grid lg:grid-cols-2 md:col-span-2 lg:col-span-4 md:justify-between lg:justify-between ">
                <a rel="noopener noreferrer" href="/signup" className="">
                  <div className="col-start-2 m-4 bg-teal-200 text-center rounded-lg border-2 border-gray-600 shadow-xl border-solid p-4 font-medium text-gray-700 hover:text-black-500 hover:bg-teal-300">
                    <span className="text-gray-600 text-xl">I am interested in tracking my own mood ...</span>
                  </div>
                </a>
                <a rel="noopener noreferrer" href="/invite" className="">
                  <div className="col-start-2 m-4 bg-teal-200 text-center rounded-lg border-2 border-gray-600 shadow-xl border-solid p-4 font-medium text-gray-700 hover:text-black-500 hover:bg-teal-300">
                    <span className="text-gray-600 text-xl">I care about somebody and I want to help them to ...</span>
                  </div>
                </a>
              </div>
        

          </div>

        </div>
      </div>
  );
};

export default Home;
