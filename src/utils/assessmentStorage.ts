import { Preferences, Profile } from '../components/demo/MoodAssessment';

export interface StoredAssessmentData {
  moodScore: string;
  note: string;
  concerns: string[];
  preferences: Preferences | null;
  profile: Profile;
  timestamp: number;
  resultId?: string;
}

const STORAGE_KEY = 'neuro_assessment_data';

export const saveAssessmentData = (data: Partial<StoredAssessmentData>): void => {
  try {
    const existingData = getStoredAssessmentData();
    const updatedData: StoredAssessmentData = {
      ...existingData,
      ...data,
      timestamp: Date.now()
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedData));
    console.log('Assessment data saved to localStorage:', updatedData);
  } catch (error) {
    console.error('Failed to save assessment data to localStorage:', error);
  }
};

export const getStoredAssessmentData = (): StoredAssessmentData => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const data = JSON.parse(stored) as StoredAssessmentData;
      console.log('Assessment data loaded from localStorage:', data);
      return data;
    }
  } catch (error) {
    console.error('Failed to load assessment data from localStorage:', error);
  }
  
  // Return default values if no stored data or error
  return {
    moodScore: '5',
    note: '',
    concerns: [],
    preferences: {
      therapistGender: 'no_preference',
      sessionType: 'no_preference',
      works_with_gender: 'no_preference',
      priceRange: {
        min: 0,
        max: 50
      },
      workingHours: 'flexible',
      availability: true
    },
    profile: {
      dateOfBirth: undefined,
      gender: undefined,
      userLocation: undefined,
    },
    timestamp: Date.now()
  };
};

export const clearStoredAssessmentData = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    console.log('Assessment data cleared from localStorage');
  } catch (error) {
    console.error('Failed to clear assessment data from localStorage:', error);
  }
};

export const hasStoredAssessmentData = (): boolean => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored !== null;
  } catch (error) {
    console.error('Failed to check for stored assessment data:', error);
    return false;
  }
};

export const saveResultData = (resultId: string, requestData: Omit<StoredAssessmentData, 'timestamp' | 'resultId'>): void => {
  try {
    const dataWithResult: StoredAssessmentData = {
      ...requestData,
      resultId,
      timestamp: Date.now()
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataWithResult));
    console.log('Result data saved to localStorage:', dataWithResult);
  } catch (error) {
    console.error('Failed to save result data to localStorage:', error);
  }
};

export const getStoredResultId = (): string | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const data = JSON.parse(stored) as StoredAssessmentData;
      return data.resultId || null;
    }
  } catch (error) {
    console.error('Failed to get stored result ID:', error);
  }
  return null;
};
