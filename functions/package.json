{"name": "functions", "scripts": {"lint": "eslint --ext .ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions,database", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@genkit-ai/firebase": "^1.6.2", "@genkit-ai/googleai": "^1.6.2", "@genkit-ai/vertexai": "^1.6.2", "@google/generative-ai": "^0.24.1", "cors": "^2.8.5", "firebase": "^11.10.0", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "genkit": "^1.6.2", "genkitx-neo4j": "^0.5.17", "twilio": "^5.4.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "genkit-cli": "^1.6.1", "ts-node": "^10.9.2", "typescript": "^4.9.0"}, "private": true}