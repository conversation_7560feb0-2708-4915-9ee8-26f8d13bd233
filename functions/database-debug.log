WARNING: An illegal reflective access operation has occurred
WARNING: Illegal reflective access by io.netty.util.internal.ReflectionUtil (file:/Users/<USER>/.cache/firebase/emulators/firebase-database-emulator-v4.11.2.jar) to field sun.nio.ch.SelectorImpl.selectedKeys
WARNING: Please consider reporting this to the maintainers of io.netty.util.internal.ReflectionUtil
WARNING: Use --illegal-access=warn to enable warnings of further illegal reflective access operations
WARNING: All illegal access operations will be denied in a future release
12:05:25.419 [NamespaceSystem-akka.actor.default-dispatcher-5] INFO akka.event.slf4j.Slf4jLogger - Slf4jLogger started
12:05:25.526 [main] INFO com.firebase.server.forge.App$ - Listening at 127.0.0.1:9000
16:28:56.759 [Thread-0] INFO com.firebase.server.forge.App$ - Attempting graceful shutdown.
16:28:56.770 [Thread-0] INFO com.firebase.server.forge.App$ - Graceful shutdown complete.
